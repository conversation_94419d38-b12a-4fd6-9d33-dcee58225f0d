<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

// 开启错误报告（调试用）
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// 引入必要的模块文件
try {
    // 确保核心模块已加载
    if (!function_exists('get_one_website')) {
        require_once(ROOT_PATH.'source/module/website.php');
    }
    if (!function_exists('get_one_category')) {
        require_once(ROOT_PATH.'source/module/category.php');
    }
    if (!function_exists('get_one_user')) {
        require_once(ROOT_PATH.'source/module/user.php');
    }
    if (!function_exists('get_webthumb')) {
        require_once(ROOT_PATH.'source/module/prelink.php');
    }
    if (!function_exists('get_format_tags')) {
        require_once(ROOT_PATH.'module/common.php');
    }

    // 引入评论功能模块
    require_once(ROOT_PATH.'module/website_comments.php');

} catch (Exception $e) {
    error_log("siteinfo.php 模块加载失败: " . $e->getMessage());
    if (defined('DEBUG') && DEBUG) {
        die("模块加载失败: " . $e->getMessage());
    } else {
        redirect('./?mod=index');
    }
}

$pagename = '站点详细';
$pageurl = '?mod=siteinfo';
$tempfile = 'siteinfo.html';
$table = $DB->table('webdata');

$web_id = intval($_GET['wid']);
$cache_id = $web_id;

// 验证网站ID
if ($web_id <= 0) {
    redirect('./?mod=index');
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	try {
		$where = "w.web_status=3 AND w.web_id=$web_id";
		$web = get_one_website($where);
		if (!$web) {
			error_log("网站不存在或未审核: web_id=$web_id");
			unset($web);
			redirect('./?mod=index');
		}

		// 更新浏览量 - 添加错误处理
		try {
			$DB->query("UPDATE $table SET web_views=web_views+1 WHERE web_id=".$web['web_id']." LIMIT 1");
		} catch (Exception $e) {
			error_log("更新浏览量失败: " . $e->getMessage());
			// 不影响页面显示，继续执行
		}

	// 同时记录今日出站统计（VIP网站直链访问统计）
	$today = date('Y-m-d');
	$spider_table = $DB->table('spider_stats');

	// 检查今天的记录是否存在 - 添加错误处理
	try {
		$today_record = $DB->fetch_one("SELECT id FROM $spider_table WHERE stat_date = '$today'");

		if ($today_record) {
			// 更新今日出站次数
			$DB->query("UPDATE $spider_table SET total_outlinks = total_outlinks + 1, updated_at = NOW() WHERE stat_date = '$today'");
		} else {
			// 创建今日记录
			$DB->query("INSERT INTO $spider_table (stat_date, total_outlinks, created_at, updated_at) VALUES ('$today', 1, NOW(), NOW())");
		}
	} catch (Exception $e) {
		// 如果spider_stats表不存在或有其他错误，忽略错误继续执行
		// 可以记录日志但不影响页面显示
	}

		// 获取分类信息 - 添加错误处理
		try {
			$cate = get_one_category($web['cate_id']);
			if (!$cate) {
				$cate = array(
					'cate_id' => 0,
					'cate_name' => '未分类',
					'cate_keywords' => '',
					'cate_description' => ''
				);
			}
		} catch (Exception $e) {
			error_log("获取分类信息失败: " . $e->getMessage());
			$cate = array(
				'cate_id' => 0,
				'cate_name' => '未分类',
				'cate_keywords' => '',
				'cate_description' => ''
			);
		}

		// 获取用户信息 - 添加错误处理
		try {
			$user = get_one_user($web['user_id']);
			if (!$user) {
				$user = array(
					'user_id' => 0,
					'nick_name' => '未知用户',
					'user_email' => ''
				);
			}
		} catch (Exception $e) {
			error_log("获取用户信息失败: " . $e->getMessage());
			$user = array(
				'user_id' => 0,
				'nick_name' => '未知用户',
				'user_email' => ''
			);
		}

		// 优化SEO标题 - 更具描述性
		$site_name = isset($options['site_name']) ? $options['site_name'] : '网站目录';
		$seo_title = $web['web_name'] . ' - ' . $cate['cate_name'] . '网站详情 - ' . $site_name;
		$smarty->assign('site_title', $seo_title);

		// 优化关键词 - 结合网站名称、分类和标签
		$seo_keywords = array();
		if (!empty($web['web_tags'])) {
			$seo_keywords[] = $web['web_tags'];
		}
		$seo_keywords[] = $web['web_name'];
		$seo_keywords[] = $cate['cate_name'];
		$seo_keywords[] = $web['web_url'];
		$seo_keywords[] = '网站收录';
		$seo_keywords[] = '网站目录';
		if (!empty($options['site_keywords'])) {
			$seo_keywords[] = $options['site_keywords'];
		}
		$smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));

		// 优化描述 - 更丰富的描述信息
		$seo_description = '';
		if (!empty($web['web_intro'])) {
			$seo_description = $web['web_intro'];
		} else {
			$seo_description = $web['web_name'] . '是一个优质的' . $cate['cate_name'] . '网站';
		}

		// 添加统计信息到描述中
		$stats_info = array();
		if (isset($web['web_views']) && $web['web_views'] > 0) {
			$stats_info[] = '访问量' . $web['web_views'];
		}
		if (isset($web['web_grank']) && $web['web_grank'] > 0) {
			$stats_info[] = '百度收录' . $web['web_grank'];
		}

		if (!empty($stats_info)) {
			$seo_description .= '，' . implode('，', $stats_info);
		}

		$seo_description .= '。收录于' . $site_name . $cate['cate_name'] . '分类目录。';

		// 确保描述长度适中（150-160字符最佳）
		if (mb_strlen($seo_description, 'UTF-8') > 160) {
			$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
		}

		$smarty->assign('site_description', $seo_description);
		// 设置导航路径和RSS
		try {
			$smarty->assign('site_path', get_sitepath($cate['cate_mod'], $web['cate_id']).' &raquo; '.$pagename);
			$smarty->assign('site_rss', get_rssfeed($cate['cate_mod'], $web['cate_id']));
		} catch (Exception $e) {
			error_log("设置导航路径失败: " . $e->getMessage());
			$smarty->assign('site_path', $pagename);
			$smarty->assign('site_rss', '');
		}

		$smarty->assign('cate_id', $cate['cate_id']);
		$smarty->assign('cate_name', $cate['cate_name']);
		$smarty->assign('cate_keywords', $cate['cate_keywords']);
		$smarty->assign('cate_description', $cate['cate_description']);

		// 处理网站数据 - 添加安全检查
		$web['web_furl'] = format_url($web['web_url']);
		$web['web_pic'] = get_webthumb($web['web_pic']);
		$web['web_ip'] = isset($web['web_ip']) ? long2ip($web['web_ip']) : '';
		$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
		$web['web_utime'] = isset($web['web_utime']) ? date('Y-m-d', $web['web_utime']) : date('Y-m-d');

		// PageRank处理
		$web['web_prank'] = isset($web['web_grank']) ? $web['web_grank'] : 0;

		// 处理标签
		try {
			$web_tags = get_format_tags($web['web_tags']);
			$smarty->assign('web_tags', $web_tags);
		} catch (Exception $e) {
			error_log("处理标签失败: " . $e->getMessage());
			$smarty->assign('web_tags', array());
		}

		$smarty->assign('web', $web);
		$smarty->assign('user', $user);

		// 获取相关网站信息 - 添加错误处理
		try {
			$smarty->assign('prev_website', get_prev_website($web['web_id']));
			$smarty->assign('next_website', get_next_website($web['web_id']));
			$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
		} catch (Exception $e) {
			error_log("获取相关网站失败: " . $e->getMessage());
			$smarty->assign('prev_website', array());
			$smarty->assign('next_website', array());
			$smarty->assign('related_website', array());
		}

	} catch (Exception $e) {
		error_log("siteinfo.php 执行失败: " . $e->getMessage());
		if (defined('DEBUG') && DEBUG) {
			die("页面生成失败: " . $e->getMessage());
		} else {
			redirect('./?mod=index');
		}
	}
}

// 输出模板 - 添加错误处理
try {
	smarty_output($tempfile, $cache_id);
} catch (Exception $e) {
	error_log("模板输出失败: " . $e->getMessage());
	if (defined('DEBUG') && DEBUG) {
		echo "<h1>模板渲染失败</h1>";
		echo "<p>错误信息: " . htmlspecialchars($e->getMessage()) . "</p>";
		echo "<p>请检查模板文件是否存在且格式正确</p>";
		echo "<p><a href='test_siteinfo_simple.php?wid=$web_id'>使用简化测试页面</a></p>";
	} else {
		redirect('./?mod=index');
	}
}
?>