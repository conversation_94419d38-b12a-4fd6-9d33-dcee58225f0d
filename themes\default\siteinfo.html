<!DOCTYPE HTML>
<html>
<head>
<title>{#$web.web_url#} - {#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="author" content="{#$options.site_name#}" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="apple-mobile-web-app-title" content="{#$web.web_name#} - {#$options.site_name#}" />
<meta name="theme-color" content="#007bff" />
<link rel="canonical" href="{#$site_url#}?mod=siteinfo&wid={#$web.web_id#}" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=siteinfo&wid={#$web.web_id#}" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />

<!-- SEO优化 - 结构化数据：网站详情 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{#$web.web_name#}",
    "url": "http://{#$web.web_url#}",
    "description": "{#$web.web_intro|escape:'javascript'#}",
    "inLanguage": "zh-CN",
    "datePublished": "{#$web.web_ctime#}",
    "dateModified": "{#$web.web_utime#}",
    "publisher": {
        "@type": "Organization",
        "name": "{#$options.site_name#}",
        "url": "{#$site_url#}"
    },
    "mainEntity": {
        "@type": "Thing",
        "name": "{#$web.web_name#}",
        "url": "http://{#$web.web_url#}",
        "description": "{#$web.web_intro|escape:'javascript'#}"
    }
}
</script>

<!-- SEO优化 - 结构化数据：面包屑导航 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "首页",
            "item": "{#$site_url#}"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "网站目录",
            "item": "{#$site_url#}?mod=webdir"
        },
        {
            "@type": "ListItem",
            "position": 3,
            "name": "{#$cate_name#}",
            "item": "{#$site_url#}?mod=webdir&cid={#$cate_id#}"
        },
        {
            "@type": "ListItem",
            "position": 4,
            "name": "{#$web.web_name#}",
            "item": "{#$site_url#}?mod=siteinfo&wid={#$web.web_id#}"
        }
    ]
}
</script>

{#include file="script.html"#}
<script type="text/javascript" src="{#$site_root#}themes/default/skin/svg-fix.js"></script>
<script type="text/javascript" src="{#$site_root#}public/js/logo-optimizer.js"></script>
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="siteinfo">
            	<h1 class="wtitle">
    <span style="float: right; margin-top: 5px;">  <!-- 浮动到右侧，并添加少量外边距调整布局 -->
        <a href="/go.php?url=http://{#$web.web_url#}" target="_blank" onClick="clickout({#$web.web_id#})" class="access-button">
            访问站点
        </a>
    </span>
    <a href="/go.php?url=http://{#$web.web_url#}" target="_blank" onClick="clickout({#$web.web_id#})" title="访问{#$web.web_name#}官方网站" rel="nofollow"><em style="color: #f60;">{#$web.web_name#}-官网入口</em></a>  <!-- 这是原有的内容，我假设它应该保留在左侧 -->
</h1>
					    <ul class="wdata" itemscope itemtype="https://schema.org/WebSite">
    <li class="line"><em><img src="module/dr_badge.php?domain={#$web.web_url#}" alt="{#$web.web_name#}域名权威度DR指数" title="{#$web.web_name#}网站DR评分"></em>DR指数</li>
    <li class="line"><em style="color: #f00;" itemprop="interactionStatistic" itemscope itemtype="https://schema.org/InteractionCounter"><meta itemprop="interactionType" content="https://schema.org/ViewAction"><span itemprop="userInteractionCount">{#$web.web_views#}</span></em>人气指数</li>
    <li class="line"><em style="color: #083;" title="百度搜索引擎收录页面数量">{#$web.web_grank#}</em>百度收录量</li>
    <li class="line"><em style="color: #083;" title="必应搜索引擎收录页面数量">{#$web.web_brank#}</em>必应收录量</li>
    <li class="line"><em style="color: #083;" title="360搜索引擎收录页面数量">{#$web.web_srank#}</em>360收录量</li>
    <li class="line"><em style="color: #083;" title="搜狗搜索引擎收录页面数量">{#$web.web_arank#}</em>搜狗收录量</li>
    <li class="line"><em id="website-status" class="status-checking" title="网站当前访问状态检测">检测中...</em>网站状态</li>
    <li class="line"><em id="response-time" class="status-checking" title="网站响应速度检测">检测中...</em>响应时间</li>
    <li class="line"><em title="从本站点击进入该网站的次数">{#$web.web_instat#}</em>入站次数</li>
    <li class="line"><em title="从该网站点击进入本站的次数">{#$web.web_outstat#}</em>出站次数</li>
    <li class="line"><em itemprop="datePublished" title="网站被收录到本目录的日期">{#$web.web_ctime#}</em>收录日期</li>
    <li class="line"><em style="color: #f60;" itemprop="dateModified" title="网站信息最后更新日期">{#$web.web_utime#}</em>更新日期</li>
</ul>

				<div class="clearfix params">
					<a href="{#$web.web_links#}" target="_blank" title="查看{#$web.web_name#}网站截图"><img src="{#$web.web_pic#}" width="130" height="110" alt="{#$web.web_name#}网站截图预览" class="wthumb" itemprop="image" /></a>
					<ul class="siteitem" itemscope itemtype="https://schema.org/WebSite">
						<li>
    <strong>网站地址：</strong>
    <!-- 目标网址：一开始就隐藏 -->
<a id="website-link"
   href="/go.php?url=http://{#$web.web_url#}"
   target="_blank"
   class="visit"
   onClick="clickout({#$web.web_id#})"
   style="display:none;">            <!-- &larr; 新增 display:none -->
  <font color="#008000">{#$web.web_url#}</font>
</a>

<!-- 占位提示：初始为“检测中...” -->
<span id="website-link-placeholder"
      style="color:#666;margin-left:4px;">链接检测中…</span>

    <!-- 网站认领按钮 -->
    <a href="/member/?mod=claim" target="_blank">
        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#08F;border-radius:1em;float:right;width:80px;">
            网站认领
        </span>
    </a>

    <!-- 违规举报按钮（新增） -->
    <a href="https://www.95dir.com/feedback/" target="_blank">
        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#F44336;border-radius:1em;float:right;width:80px;margin-left:6px;">
            违规举报
        </span>
    </a>
</li>
            			<li><strong>服务器IP：</strong><span itemprop="serverIP">{#$web.web_ip#}</span></li>
                        <li><strong>网站描述：</strong><span style="line-height: 23px;" itemprop="description">{#$web.web_intro#}</span></li>
                        <li><strong>综合权重：</strong><span><img src="https://baidurank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="百度移动权重" /></span>
<span><img src="https://baidurank.aizhan.com/api/mbr?domain={#$web.web_url#}&style=images" alt="百度权重" /></span>
<span><img src="https://sogourank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="搜狗权重" /></span>
<span><img src="https://sorank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="360权重" /></span>
<span><img src="https://smrank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="神马权重" /></span>
<span><img src="https://bingrank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="必应权重" /></span>
<span><img src="https://toutiaorank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="头条权重" /></span></li>
                        <li><strong>备案信息：</strong><script type="text/javascript" src="https://icp.aizhan.com/geticp/?host={#$web.web_url#}&amp;style=text" charset="utf-8"></script></li>
                        <li><strong>联系站长：</strong><a href="http://wpa.qq.com/msgrd?v=3&amp;uin={#$user.user_qq#}&amp;site={#$user.nick_name#}&amp;menu=yes" target="_blank"><img border="0" alt="点击这里给我发消息" src="http://wpa.qq.com/pa?p=2:{#$user.user_qq#}:41"></a></li>
                        <li><strong>TAG标签：</strong>{#foreach from=$web_tags item=item#}<a href="{#$item.tag_link#}" title="查看更多{#$item.tag_name#}相关网站" itemprop="keywords">{#$item.tag_name#}</a>　{#/foreach#}</li>
                        <li>{#get_adcode(1)#}</li>
                        <li><strong>相关查询：</strong> <a rel="external nofollow" href="https://www.aizhan.com/cha/{#$web.web_url#}" target="_blank" title="{#$web.web_name#}">SEO综合查询</a> |
    <a rel="external nofollow" href="https://pr.aizhan.com/{#$web.web_url#}/" target="_blank" title="{#$web.web_name#}">Google PageRank</a> |
    <a rel="external nofollow" href="https://pr.aizhan.com/{#$web.web_url#}/" target="_blank" title="{#$web.web_name#}">Sogou Rank</a> |
    <a rel="external nofollow" href="https://rank.aizhan.com/{#$web.web_url#}" target="_blank" title="{#$web.web_name#}">网站权重查询</a> |
    <a rel="external nofollow" href="http://www.baidu.com/s?wd=site%3A{#$web.web_url#}&amp;cl=3" target="_blank" title="{#$web.web_name#}">百度查询</a> |
    <a rel="external nofollow" href="http://www.so.com/s?q=site:{#$web.web_url#}" target="_blank" title="{#$web.web_name#}">360搜索查询</a> |
    <a rel="external nofollow" href="http://www.soso.com/q?w=site%3A{#$web.web_url#}&amp;sc=web&amp;ch=w.ptl&amp;lr=chs" target="_blank" title="{#$web.web_name#}">SOSO查询</a> |
    <a rel="external nofollow" href="http://www.sogou.com/web?query=site%3A{#$web.web_url#}" target="_blank" title="{#$web.web_name#}">搜狗查询</a> |
    <a rel="external nofollow" href="http://cn.bing.com/search?q=site%3A{#$web.web_url#}&amp;go=&amp;form=QBLH" target="_blank" title="{#$web.web_name#}">必应查询</a> |
    <a rel="external nofollow" href="http://search.aol.com/aol/search?s_chn=prt_ct18&amp;enabled_terms=&amp;s_it=comsearch&amp;q=site%3A{#$web.web_url#}" target="_blank" title="{#$web.web_name#}">AOL搜索</a> |
    <a rel="external nofollow" href="http://www.google.com.hk/search?hl=zh-CN&amp;q=site%3A{#$web.web_url#}" target="_blank" title="{#$web.web_name#}">谷歌查询</a> |
    <a rel="external nofollow" href="https://icp.aizhan.com/{#$web.web_url#}/" target="_blank" title="{#$web.web_name#}">网站备案查询</a> |
    <a rel="external nofollow" href="https://baidurank.aizhan.com/baidu/{#$web.web_url#}/" target="_blank" title="{#$web.web_name#}">百度权重查询</a> |
    <a rel="external nofollow" href="https://linkche.aizhan.com/{#$web.web_url#}/" target="_blank" title="{#$web.web_name#}">友情链接检查</a> |
    <a rel="external nofollow" href="https://ahrefs.com/website-authority-checker/?input={#$web.web_url#}" target="_blank" title="{#$web.web_name#}">域名DR查询</a> |
    <a rel="external nofollow" href="https://zhenzhan.baidu.com/#/mnt/detail?kw={#$web.web_url#}" target="_blank" title="{#$web.web_name#}">百度网站检测</a></li>
<li><strong>本页地址：</strong><a href="{#$site_url#}?mod=siteinfo&wid={#$web.web_id#}">{#$site_url#}?mod=siteinfo&wid={#$web.web_id#}</a></li>
					</ul>
				</div>
            </div>
            <div class="blank10"></div>

            <!-- SEO内链优化：相关导航链接 -->
            <div class="seo-internal-links" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 12px 0; font-size: 16px; color: #333; font-weight: bold;">
                    <i class="fas fa-link" style="color: #007bff; margin-right: 8px;"></i>相关导航
                </h4>
                <div style="line-height: 2.2; font-size: 14px;">
                    <a href="?mod=webdir&cid={#$cate_id#}" title="浏览更多{#$cate_name#}网站" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-folder" style="margin-right: 5px;"></i>更多{#$cate_name#}网站
                    </a>
                    <a href="?mod=category" title="网站分类目录" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-sitemap" style="margin-right: 5px;"></i>网站分类
                    </a>
                    <a href="?mod=update" title="最新收录网站" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-clock" style="margin-right: 5px;"></i>最新收录
                    </a>
                    <a href="?mod=top" title="热门网站排行榜" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-trophy" style="margin-right: 5px;"></i>热门排行
                    </a>
                    {#if $web.web_ispay#}
                    <a href="?mod=vip_list" title="VIP网站列表" style="color: #ffd700; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-crown" style="margin-right: 5px;"></i>VIP网站
                    </a>
                    {#/if#}
                    <a href="?mod=article" title="站长资讯" style="color: #007bff; text-decoration: none;">
                        <i class="fas fa-newspaper" style="margin-right: 5px;"></i>站长资讯
                    </a>
                </div>
            </div>

            <div class="web_ai_intro">关于【{#$web.web_name#}】的详细介绍</div>
            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
        	    <span><img src="https://cdn.iocdn.cc/mshots/v1/{#$web.web_furl#}" width="100%" height="50%" alt="{#$web.web_name#}网站首页截图" title="{#$web.web_name#}网站首页预览" itemprop="screenshot" loading="lazy" /></span>
        	    <div class="blank10"></div>
				{#$web.web_ai_intro#}
			</div>

			<div class="blank10"></div>
            <div class="web_ai_intro">注意事项：凡违反中国国家法律法规的网站，95分类目录一律不给予收录。</div>
            <div class="blank10"></div>
			<div id="relsite" class="clearfix">
<p><a href="{#$web.web_links#}" title="访问{#$web.web_name#}官方网站">{#$web.web_name#}</a>于{#$web.web_ctime#}被<strong>95目录网</strong>收录在<a href="?mod=webdir&cid={#$cate_id#}" title="{#$cate_name#}网站分类目录">{#$cate_name#}分类目录</a>中。该网站相关信息来自互联网或网友推荐分享，我们致力于为用户提供优质的<strong>网站收录服务</strong>和<strong>网站推荐</strong>。</p>

<p style="margin-top: 15px; padding: 10px; background: #fff3cd; border-left: 4px solid #ffc107; font-size: 14px;">
<strong>收录说明：</strong>95目录网作为专业的<strong>网站分类目录平台</strong>，严格审核每个提交的网站。由于网站内容具有动态性，我们无法保证所收录网站内容的实时准确性。用户在访问<a href="{#$web.web_links#}" title="{#$web.web_name#}">{#$web.web_name#}</a>时，请谨慎判断网站内容的真实性和合法性。如发现网址失效、内容违规等问题，请及时<a href="?mod=feedback" title="意见反馈">联系我们</a>处理。
</p>

<p style="margin-top: 15px; font-size: 13px; color: #666; line-height: 1.6;">
<strong>相关标签：</strong>
{#foreach from=$web_tags item=tag name=tags#}
<a href="{#$tag.tag_link#}" title="查看更多{#$tag.tag_name#}相关网站" style="color: #007bff; margin-right: 10px;">{#$tag.tag_name#}</a>
{#/foreach#}
<a href="?mod=webdir&cid={#$cate_id#}" title="{#$cate_name#}网站大全" style="color: #007bff; margin-right: 10px;">{#$cate_name#}网站</a>
<a href="?mod=webdir" title="网站目录" style="color: #007bff; margin-right: 10px;">网站目录</a>
<a href="?mod=update" title="最新收录网站" style="color: #007bff;">最新收录</a>
</p>
</div>



<!-- 上一站下一站导航 -->
<div class="website-navigation" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
	<div style="display: flex; justify-content: space-between; align-items: center;">
		<div class="prev-website" style="flex: 1; text-align: left;">
			{#if $prev_website#}
				<a href="{#$prev_website.web_link#}" target="_blank" style="color: #007bff; text-decoration: none; font-size: 14px;"
				   onmouseover="this.style.color='#0056b3'" onmouseout="this.style.color='#007bff'">
					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
					上一站：{#$prev_website.web_name#}
				</a>
			{#else#}
				<span style="color: #6c757d; font-size: 14px;">
					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
					上一站：暂无
				</span>
			{#/if#}
		</div>

		<div class="navigation-center" style="flex: 0 0 auto; margin: 0 20px;">
			<span style="color: #6c757d; font-size: 12px;">网站导航</span>
		</div>

		<div class="next-website" style="flex: 1; text-align: right;">
			{#if $next_website#}
				<a href="{#$next_website.web_link#}" target="_blank" style="color: #007bff; text-decoration: none; font-size: 14px;"
				   onmouseover="this.style.color='#0056b3'" onmouseout="this.style.color='#007bff'">
					下一站：{#$next_website.web_name#}
					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
				</a>
			{#else#}
				<span style="color: #6c757d; font-size: 14px;">
					下一站：暂无
					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
				</span>
			{#/if#}
		</div>
	</div>
</div>
<div class="text-xs text-muted"><div><span>©</span> 版权声明</div><div class="posts-copyright"><div><br><fieldset style="border:1px dashed #008CFF;padding:10px;border-radius:8px;line-height: 2em;color: #6D6D6D"><legend align="center" style="color:#FFFFFF;width:200px;text-align:center;background-color:#008CFF;font-size: 14px;border-radius: 5px">95分类目录 - 版权声明</legend>1、本主题所有言论和图片纯属会员个人意见，与本站立场无关。<br> 2、本站所有主题由该文章作者发表，该文章作者与<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>享有文章相关版权。<br> 3、其他单位或个人使用、转载或引用本文时必须同时征得该文章作者和<a href="https://www.95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>的同意。<br> 4、文章作者须承担一切因本文发表而直接或间接导致的民事或刑事法律责任。<br> 5、本帖部分内容转载自其它媒体，但并不代表本站赞同其观点和对其真实性负责。<br> 6、如本帖侵犯到任何版权问题，请立即告知本站，本站将及时予与删除并致以最深的歉意。<br> 7、<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>管理员有权不事先通知发贴者而删除本文。</fieldset><br></div></div></div>



<div class="blank10"></div>


				    <!-- 打赏按钮 -->
				    <div class="donate-button-container">
<button onclick="showDonatePopup()">我要上推荐</button>
<div class="fenxiang">
    <div class="social-share">
    </div>
    </div>
</div>
<!-- 打赏弹窗 -->
<div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
<div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
<span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
<h3 style="color: #4A90E2; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
    推荐服务价格表
</h3>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p style="margin: 0; line-height: 1.6; text-align: center;">
        <strong style="color: #28a745;">10元上推荐位</strong> - 首页推荐展示<br>
        <strong style="color: #E94E77;">VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
        <strong style="color: #ff6600;">5元快审服务</strong> - 1-3个工作日审核
    </p>
</div>
<p style="text-align: center; margin: 15px 0; color: #666;">
    备注格式：<strong style="color: #F39C12;">推荐/vip/快审+网址</strong>
</p>
<div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
<div style="text-align: center;">
<h4>微信支付</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="95目录网微信支付二维码 - 网站收录付费服务" title="微信扫码支付网站收录费用" style="width: 150px; height: 150px;" loading="lazy">
</div>
<div style="text-align: center;">
<h4>支付宝支付</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="95目录网支付宝支付二维码 - 网站收录付费服务" title="支付宝扫码支付网站收录费用" style="width: 150px; height: 150px;" loading="lazy">
</div>
</div>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
    <h4 style="margin-top: 0; color: #333;">服务说明：</h4>
    <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
        <li>推荐位：展示在首页推荐区域</li>
        <li>VIP位：展示在顶部VIP推广区</li>
        <li>快审：1-3个工作日完成审核</li>
        <li>付款后请联系客服提供网站信息</li>
    </ul>
</div>
</div>
</div>


            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
            	<h2>相关站点</h2>
               	<ul class="rellist" itemscope itemtype="https://schema.org/ItemList">
              		{#foreach from=$related_website item=rel#}
               		<li itemprop="itemListElement" itemscope itemtype="https://schema.org/WebSite"><a href="{#$rel.web_link#}" title="访问{#$rel.web_name#}网站" itemprop="url"><img src="{#$rel.web_pic#}" width="100" height="80" alt="{#$rel.web_name#}网站截图" itemprop="image" /><strong itemprop="name">{#$rel.web_name#}</strong></a></li>
               		{#/foreach#}
              	</ul>
            </div>

            <div class="blank10"></div>
            <div id="relsite" class="clearfix">
            	<h2>本类排行榜</h2>
               	<ul class="rellist" itemscope itemtype="https://schema.org/ItemList">
              		{#foreach from=get_websites($web.cate_id, 10, false, false, 'views') item=hot name=hot_website#}
            <li itemprop="itemListElement" itemscope itemtype="https://schema.org/WebSite"><a href="{#$hot.web_link#}" title="访问{#$hot.web_name#}网站" itemprop="url"><img src="{#$hot.web_pic#}" width="100" height="80" alt="{#$hot.web_name#}网站截图" itemprop="image" /><strong itemprop="name">{#$hot.web_name#}</strong></a></li>
            {#/foreach#}
              	</ul>
            </div>

            <!-- 网站点评功能 -->
            <div class="blank10"></div>
            <div id="website-comments" class="clearfix">
                <h2 style="color: #667eea;">💬 网站点评</h2>

                <!-- 评分统计 -->
                <div id="comment-stats" class="comment-stats" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="text-align: center; color: #666;">
                        <span id="loading-stats">正在加载评分统计...</span>
                    </div>
                </div>

                <!-- 评论表单 -->
                <div class="comment-form" style="background: #fff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="margin-top: 0; color: #333; font-size: 16px;">发表点评</h3>
                    <form id="comment-form">
                        <input type="hidden" name="web_id" value="{#$web.web_id#}">

                        <!-- 星级评分 -->
                        <div class="rating-section" style="margin-bottom: 15px;">
                            <div class="rating-item" style="margin-bottom: 10px;">
                                <label style="display: inline-block; width: 80px; font-weight: bold;">内容质量：</label>
                                <div class="star-rating" data-rating="content_quality" style="display: inline-block;">
                                    <span class="star" data-value="1">★</span>
                                    <span class="star" data-value="2">★</span>
                                    <span class="star" data-value="3">★</span>
                                    <span class="star" data-value="4">★</span>
                                    <span class="star" data-value="5">★</span>
                                </div>
                                <input type="hidden" name="content_quality" value="5">
                            </div>

                            <div class="rating-item" style="margin-bottom: 10px;">
                                <label style="display: inline-block; width: 80px; font-weight: bold;">网站服务：</label>
                                <div class="star-rating" data-rating="service_quality" style="display: inline-block;">
                                    <span class="star" data-value="1">★</span>
                                    <span class="star" data-value="2">★</span>
                                    <span class="star" data-value="3">★</span>
                                    <span class="star" data-value="4">★</span>
                                    <span class="star" data-value="5">★</span>
                                </div>
                                <input type="hidden" name="service_quality" value="5">
                            </div>

                            <div class="rating-item" style="margin-bottom: 15px;">
                                <label style="display: inline-block; width: 80px; font-weight: bold;">网站诚信：</label>
                                <div class="star-rating" data-rating="trust_level" style="display: inline-block;">
                                    <span class="star" data-value="1">★</span>
                                    <span class="star" data-value="2">★</span>
                                    <span class="star" data-value="3">★</span>
                                    <span class="star" data-value="4">★</span>
                                    <span class="star" data-value="5">★</span>
                                </div>
                                <input type="hidden" name="trust_level" value="5">
                            </div>
                        </div>

                        <!-- 评论内容 -->
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">评论内容：</label>
                            <textarea name="comment_content" placeholder="请分享您对这个网站的使用体验，内容不允许包含域名链接..."
                                      style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-family: inherit;"
                                      maxlength="500"></textarea>
                            <div style="text-align: right; font-size: 12px; color: #999; margin-top: 5px;">
                                <span id="char-count">0</span>/500字符
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div style="text-align: center;">
                            <button type="submit" style="background: #007bff; color: white; padding: 10px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                                发表评论
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 评论列表 -->
                <div id="comments-list" class="comments-list">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        正在加载评论...
                    </div>
                </div>
            </div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>
            <div class="blank10"></div>-->

            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 10, 'views') item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}网站logo - VIP优质网站推荐" title="{#$quick.web_name#}网站截图预览" loading="lazy" /></a><strong><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li>[<em><a href="{#$art.cate_link#}" title="{#$art.cate_name#}">{#$art.cate_name#}</a></em>]<a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>


            <div class="blank10"></div>
            <div id="bestart">
                <h3>最新收录</h3>
            <ul class="artlist_b">
                    {#foreach from=get_websites(0, 8) item=new#}
					<li data-number="{#$idx+1#}">[<em><a href="{#$new.cate_link#}" title="{#$new.cate_name#}">{#$new.cate_name#}</a></em>]<a href="{#$new.web_link#}" title="{#$new.web_name#}">{#$new.web_name#}</a><span>{#$new.web_ctime#}</span></li>
                   	{#/foreach#}
                </ul>
            </div>


            <div class="blank10"></div>
            <div id="bestweb">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}网站logo - 推荐优质网站" title="{#$best.web_name#}网站截图预览" loading="lazy" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="/go.php?url=http://{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

<script type="text/javascript" src="{#$site_root#}?mod=getdata&type=ip&wid={#$web.web_id#}"></script>
<script type="text/javascript" src="{#$site_root#}?mod=getdata&type=grank&wid={#$web.web_id#}"></script>
<script type="text/javascript" src="{#$site_root#}?mod=getdata&type=brank&wid={#$web.web_id#}"></script>
<script type="text/javascript" src="{#$site_root#}?mod=getdata&type=srank&wid={#$web.web_id#}"></script>
<script type="text/javascript" src="{#$site_root#}?mod=getdata&type=arank&wid={#$web.web_id#}"></script>
<script type="text/javascript" src="{#$site_root#}?mod=getdata&type=clink&wid={#$web.web_id#}"></script>
<script>
const checkWebsiteStatus = async () => {
  const statusEl = document.getElementById('website-status');
  const timeEl   = document.getElementById('response-time');
  const linkEl   = document.getElementById('website-link');
  const phEl     = document.getElementById('website-link-placeholder');
  const target   = '{#$web.web_furl#}';

  /* —— 1. 开始检测 —— */
  if (linkEl) linkEl.style.display = 'none';
  if (phEl) {
    phEl.textContent = '链接检测中…';
    phEl.style.color = '#666';
    phEl.style.display = 'inline';
  }
  statusEl.textContent = '检测中...';
  statusEl.style.color = '#666';
  timeEl.textContent   = '检测中...';

  try {
    const res  = await fetch(`/module/status_check.php?url=${encodeURIComponent(target)}`);
    const data = await res.json();
    if (data.error) throw new Error(data.error);

    /* —— 2. 解析状态码 —— */
    const map = {
      200: ['正常200', '#083'],
      301: ['跳转301', '#083'],
      302: ['跳转302', '#083'],
      403: ['禁止访问403',   '#f00'],
      404: ['未找到404',     '#f00'],
      500: ['服务器错误500', '#f00'],
      503: ['服务不可用503', '#f00']
    };
    const [txt, clr] = map[data.status] || [`异常(${data.status})`, '#f00'];

    statusEl.textContent = txt;
    statusEl.style.color = clr;
    timeEl.textContent   = `${data.response_time}ms`;
    timeEl.style.color   = data.response_time > 3000 ? '#f60' : '#666';

    /* —— 3. 根据状态码显示/隐藏链接 —— */
    const hideCodes = [403, 404, 500, 503];
    if (hideCodes.includes(data.status)) {
      // 异常 &rarr; 隐藏链接，提示对应中文文案
      if (linkEl) linkEl.style.display = 'none';
      if (phEl) {
        phEl.textContent = txt;   // 直接用中文文案
        phEl.style.color = '#f00';
        phEl.style.display = 'inline';
      }
    } else {
      // 正常 &rarr; 显示链接，隐藏提示
      if (linkEl) linkEl.style.display = '';
      if (phEl) phEl.style.display = 'none';
    }

  } catch (e) {
    /* —— 4. 检测失败 —— */
    if (linkEl) linkEl.style.display = 'none';
    if (phEl) {
      phEl.textContent = '链接检测失败';
      phEl.style.color = '#f00';
      phEl.style.display = 'inline';
    }
    statusEl.textContent = '链接检测失败';
    statusEl.style.color = '#f00';
    timeEl.textContent   = '';
  }
};

/* 页面加载即检测，并每 5 分钟重检 */
checkWebsiteStatus();
setInterval(checkWebsiteStatus, 300000);
</script>

<!-- 评论功能样式 -->
<style>
.star-rating {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    user-select: none;
    display: inline-block;
}

.star-rating .star {
    display: inline-block;
    transition: color 0.2s;
    margin-right: 3px;
    cursor: pointer;
    padding: 2px;
    line-height: 1;
    position: relative;
    z-index: 1;
}

.star-rating .star {
    color: #ddd;
}

.star-rating .star:hover,
.star-rating .star.active {
    color: #ffc107 !important;
    text-shadow: 0 0 3px rgba(255, 193, 7, 0.5);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .star-rating {
        font-size: 28px;
    }

    .star-rating .star {
        padding: 5px;
        margin-right: 5px;
    }
}

@media (max-width: 480px) {
    .star-rating {
        font-size: 32px;
    }

    .star-rating .star {
        padding: 8px;
        margin-right: 8px;
    }

    .rating-item label {
        display: block;
        margin-bottom: 8px;
        width: 100%;
    }
}

.comment-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.comment-user {
    font-weight: bold;
    color: #333;
}

.comment-user.member {
    color: #007bff;
}

.comment-time {
    font-size: 12px;
    color: #999;
}

.comment-ratings {
    margin-bottom: 10px;
    font-size: 14px;
}

.comment-ratings span {
    margin-right: 15px;
    color: #666;
}

.rating-row {
    display: block;
    margin-bottom: 5px;
    color: #666;
    line-height: 1.4;
}

/* 桌面端：保持原有的横向布局 */
@media (min-width: 769px) {
    .comment-ratings {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .rating-row {
        display: inline-block;
        margin-bottom: 0;
        margin-right: 15px;
    }
}

/* 移动端优化：强制三行显示 */
@media (max-width: 768px) {
    .comment-ratings {
        margin-bottom: 12px;
    }

    .rating-row {
        display: block;
        margin-bottom: 6px;
        font-size: 13px;
        width: 100%;
    }

    /* 隐藏旧的span布局，只在移动端显示rating-row */
    .comment-ratings span {
        display: none;
    }

    .comment-ratings .rating-row {
        display: block;
    }
}

.comment-content {
    line-height: 1.6;
    color: #333;
    margin-bottom: 10px;
}

.comment-actions {
    text-align: right;
}

.reply-btn {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.reply-btn:hover {
    background: #007bff;
    color: white;
}

.reply-form {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
    display: none;
}

.reply-item {
    background: #f8f9fa;
    border-left: 3px solid #007bff;
    padding: 10px 15px;
    margin-top: 10px;
    border-radius: 0 4px 4px 0;
}

.stats-item {
    display: inline-block;
    margin-right: 20px;
    text-align: center;
}

.stats-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.stats-value {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.stats-stars {
    font-size: 14px;
    margin-top: 2px;
}

.stats-stars span {
    margin-right: 1px;
}

/* 统计信息移动端优化 */
@media (max-width: 768px) {
    .stats-item {
        display: block;
        margin-right: 0;
        margin-bottom: 15px;
        text-align: left;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }

    .stats-label {
        font-size: 13px;
        margin-bottom: 3px;
    }

    .stats-value {
        font-size: 15px;
    }

    .stats-stars {
        font-size: 13px;
    }
}
</style>

<!-- 评论功能脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化评论功能...');

    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('JavaScript错误:', event.error);
        console.error('错误位置:', event.filename, ':', event.lineno);

        // 如果是评论相关的错误，显示友好提示
        if (event.filename && event.filename.includes('siteinfo')) {
            const commentsListElement = document.getElementById('comments-list');
            if (commentsListElement && !commentsListElement.innerHTML.includes('评论功能暂时不可用')) {
                commentsListElement.innerHTML =
                    '<div style="text-align: center; color: #dc3545; padding: 20px;">' +
                    '评论功能暂时不可用，请刷新页面重试' +
                    '</div>';
            }
        }

        // 不阻止默认错误处理
        return false;
    });

    // 延迟初始化，确保DOM完全准备好
    setTimeout(function() {
        try {
            // 检查管理员权限
            checkAdminPermission();

            // 初始化星级评分
            initStarRating();

            // 初始化字符计数
            initCharCount();

            // 加载评论
            loadComments();

            // 绑定表单提交
            bindCommentForm();
        } catch (error) {
            console.error('初始化评论功能时发生错误:', error);
            const commentsListElement = document.getElementById('comments-list');
            if (commentsListElement) {
                commentsListElement.innerHTML =
                    '<div style="text-align: center; color: #dc3545; padding: 20px;">' +
                    '评论功能初始化失败，请刷新页面重试' +
                    '</div>';
            }
        }
    }, 200);
});

// 初始化星级评分
function initStarRating() {
    console.log('开始初始化星级评分...');

    // 直接处理每个评分类型
    const ratingTypes = ['content_quality', 'service_quality', 'trust_level'];

    ratingTypes.forEach(ratingName => {
        const starRating = document.querySelector(`.star-rating[data-rating="${ratingName}"]`);
        const hiddenInput = document.querySelector(`input[name="${ratingName}"]`);

        if (!starRating || !hiddenInput) {
            console.log(`跳过 ${ratingName}: 找不到星级容器或隐藏输入框`);
            return;
        }

        console.log(`初始化 ${ratingName}`);

        const stars = starRating.querySelectorAll('.star');

        // 默认显示为灰色星星，不点亮任何星星
        // 将隐藏输入框的值设置为0，表示未评分
        hiddenInput.value = 0;
        // 不调用 updateStars，保持所有星星为灰色

        // 为每个星星添加事件
        stars.forEach((star, index) => {
            // 移除旧的事件监听器
            const newStar = star.cloneNode(true);
            star.parentNode.replaceChild(newStar, star);
        });

        // 重新获取星星并绑定事件
        const newStars = starRating.querySelectorAll('.star');

        newStars.forEach((star, index) => {
            const value = index + 1;

            // 点击事件
            star.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                updateStars(newStars, value);
                hiddenInput.value = value;
                console.log(`设置 ${ratingName} 为 ${value} 星`);
                return false;
            };

            // 鼠标悬停事件
            star.onmouseover = function() {
                updateStars(newStars, value);
            };

            // 触摸事件
            star.ontouchstart = function(e) {
                e.preventDefault();
                updateStars(newStars, value);
                hiddenInput.value = value;
                console.log(`触摸设置 ${ratingName} 为 ${value} 星`);
                return false;
            };
        });

        // 鼠标离开事件
        starRating.onmouseleave = function() {
            const currentValue = parseInt(hiddenInput.value) || 0;
            updateStars(newStars, currentValue);
        };

        console.log(`${ratingName} 初始化完成`);
    });

    console.log('星级评分初始化完成');
}

// 备用初始化方法 - 直接绑定到window.onload
window.addEventListener('load', function() {
    console.log('页面完全加载，尝试备用初始化...');

    // 检查是否已经初始化
    const firstStar = document.querySelector('.star-rating .star');
    if (firstStar && !firstStar.onclick) {
        console.log('检测到星级评分未初始化，执行备用初始化...');
        setTimeout(initStarRating, 100);
    }
});

// 更新星级显示
function updateStars(stars, value) {
    stars.forEach((star, index) => {
        // 先移除所有active类
        star.classList.remove('active');
        // 然后为应该点亮的星星添加active类
        if (index < value) {
            star.classList.add('active');
        }
    });
    console.log(`更新星级显示: ${value} 星`);
}

// 初始化字符计数
function initCharCount() {
    const textarea = document.querySelector('textarea[name="comment_content"]');
    const charCount = document.getElementById('char-count');

    textarea.addEventListener('input', () => {
        const length = textarea.value.length;
        charCount.textContent = length;

        if (length > 500) {
            charCount.style.color = '#dc3545';
        } else if (length > 450) {
            charCount.style.color = '#ffc107';
        } else {
            charCount.style.color = '#999';
        }
    });
}

// 加载评论
function loadComments() {
    try {
        const webIdInput = document.querySelector('input[name="web_id"]');
        if (!webIdInput) {
            console.error('找不到web_id输入框');
            document.getElementById('comments-list').innerHTML =
                '<div style="text-align: center; color: #dc3545; padding: 20px;">页面初始化错误</div>';
            return;
        }

        const webId = webIdInput.value;
        if (!webId) {
            console.error('web_id为空');
            document.getElementById('comments-list').innerHTML =
                '<div style="text-align: center; color: #999; padding: 20px;">暂无评论</div>';
            return;
        }

        fetch('module/comment_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_comments&web_id=${webId}&limit=20`
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    displayComments(data.comments || []);
                    displayStats(data.stats || {});
                } else {
                    console.warn('获取评论失败:', data.message);
                    document.getElementById('comments-list').innerHTML =
                        '<div style="text-align: center; color: #999; padding: 20px;">暂无评论</div>';
                }
            } catch (parseError) {
                console.error('JSON解析错误:', parseError);
                console.error('服务器响应:', text);
                document.getElementById('comments-list').innerHTML =
                    '<div style="text-align: center; color: #dc3545; padding: 20px;">数据格式错误</div>';
            }
        })
        .catch(error => {
            console.error('加载评论失败:', error);
            document.getElementById('comments-list').innerHTML =
                '<div style="text-align: center; color: #dc3545; padding: 20px;">加载评论失败，请刷新页面重试</div>';
        });
    } catch (error) {
        console.error('loadComments函数执行错误:', error);
        document.getElementById('comments-list').innerHTML =
            '<div style="text-align: center; color: #dc3545; padding: 20px;">系统错误，请刷新页面</div>';
    }
}

// 显示评论统计
function displayStats(stats) {
    if (stats.total_comments === 0) {
        document.getElementById('comment-stats').innerHTML = `
            <div style="text-align: center; color: #666;">
                <p>暂无评论数据</p>
            </div>
        `;
        return;
    }

    const statsHtml = `
        <div class="stats-item">
            <span class="stats-label">总评论数</span>
            <span class="stats-value">${stats.total_comments}</span>
        </div>
        <div class="stats-item">
            <span class="stats-label">内容质量</span>
            <span class="stats-value">${stats.avg_content_quality}</span>
            <div class="stats-stars">${generateStars(stats.avg_content_quality)}</div>
        </div>
        <div class="stats-item">
            <span class="stats-label">网站服务</span>
            <span class="stats-value">${stats.avg_service_quality}</span>
            <div class="stats-stars">${generateStars(stats.avg_service_quality)}</div>
        </div>
        <div class="stats-item">
            <span class="stats-label">网站诚信</span>
            <span class="stats-value">${stats.avg_trust_level}</span>
            <div class="stats-stars">${generateStars(stats.avg_trust_level)}</div>
        </div>
        <div class="stats-item">
            <span class="stats-label">综合评分</span>
            <span class="stats-value">${stats.avg_overall}</span>
            <div class="stats-stars">${generateStars(stats.avg_overall)}</div>
        </div>
    `;

    document.getElementById('comment-stats').innerHTML = statsHtml;
}

// 生成星级显示
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    let stars = '';

    // 实心星星
    for (let i = 0; i < fullStars; i++) {
        stars += '<span style="color: #ffc107;">★</span>';
    }

    // 半星
    if (hasHalfStar) {
        stars += '<span style="color: #ffc107;">☆</span>';
    }

    // 空星
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<span style="color: #ddd;">☆</span>';
    }

    return stars;
}

// 显示评论列表
function displayComments(comments) {
    try {
        const commentsListElement = document.getElementById('comments-list');
        if (!commentsListElement) {
            console.error('找不到评论列表容器');
            return;
        }

        if (!Array.isArray(comments) || comments.length === 0) {
            commentsListElement.innerHTML =
                '<div style="text-align: center; color: #999; padding: 20px;">暂无评论，快来发表第一个评论吧！</div>';
            return;
        }

        let html = '';
        comments.forEach((comment, index) => {
            try {
                if (comment && typeof comment === 'object') {
                    html += generateCommentHtml(comment);
                } else {
                    console.warn(`评论数据格式错误，索引: ${index}`, comment);
                }
            } catch (error) {
                console.error(`生成评论HTML失败，索引: ${index}`, error, comment);
                // 继续处理其他评论，不让一个错误影响整个列表
            }
        });

        if (html) {
            commentsListElement.innerHTML = html;
            // 绑定回复按钮事件
            bindReplyButtons();
        } else {
            commentsListElement.innerHTML =
                '<div style="text-align: center; color: #dc3545; padding: 20px;">评论数据处理失败</div>';
        }
    } catch (error) {
        console.error('displayComments函数执行错误:', error);
        const commentsListElement = document.getElementById('comments-list');
        if (commentsListElement) {
            commentsListElement.innerHTML =
                '<div style="text-align: center; color: #dc3545; padding: 20px;">显示评论时发生错误</div>';
        }
    }
}

// 生成评论HTML
function generateCommentHtml(comment) {
    try {
        // 验证评论数据
        if (!comment || typeof comment !== 'object') {
            console.error('无效的评论数据:', comment);
            return '<div class="comment-item" style="color: #dc3545; padding: 10px;">评论数据错误</div>';
        }

        // 安全地获取评论属性，提供默认值
        const commentId = comment.comment_id || 0;
        const isAdmin = window.isAdmin || false;
        const userClass = comment.is_member ? 'member' : '';
        const userDisplay = comment.is_member ?
            (comment.display_name || '会员用户') :
            `匿名 ( ${comment.display_ip || '未知IP'} )`;
        const displayTime = comment.display_time || '未知时间';
        const commentContent = comment.comment_content || '';

        // 安全地生成星级评分
        const contentQuality = comment.content_quality || 0;
        const serviceQuality = comment.service_quality || 0;
        const trustLevel = comment.trust_level || 0;

        let html = `
            <div class="comment-item">
                <div class="comment-header">
                    <span class="comment-user ${userClass}">${userDisplay}</span>
                    <span class="comment-time">${displayTime}</span>
                    ${isAdmin ? `<button class="admin-delete-btn" onclick="deleteComment(${commentId})" style="background: #dc3545; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer; margin-left: 10px; font-size: 11px;">删除</button>` : ''}
                </div>
                <div class="comment-ratings">
                    <div class="rating-row">内容质量：${generateStars(contentQuality)}</div>
                    <div class="rating-row">网站服务：${generateStars(serviceQuality)}</div>
                    <div class="rating-row">网站诚信：${generateStars(trustLevel)}</div>
                </div>
                <div class="comment-content">${commentContent}</div>
                <div class="comment-actions">
                    <button class="reply-btn" onclick="showReplyForm(${commentId})">回复</button>
                </div>
                <div class="reply-form" id="reply-form-${commentId}">
                    <textarea placeholder="请输入回复内容..." style="width: 100%; height: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                    <div style="text-align: right; margin-top: 10px;">
                        <button onclick="hideReplyForm(${commentId})" style="background: #6c757d; color: white; padding: 5px 15px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">取消</button>
                        <button onclick="submitReply(${commentId})" style="background: #007bff; color: white; padding: 5px 15px; border: none; border-radius: 4px; cursor: pointer;">提交回复</button>
                    </div>
                </div>
        `;

        // 安全地添加回复
        if (comment.replies && Array.isArray(comment.replies) && comment.replies.length > 0) {
            comment.replies.forEach((reply, index) => {
                try {
                    if (!reply || typeof reply !== 'object') {
                        console.warn(`回复数据格式错误，索引: ${index}`, reply);
                        return;
                    }

                    const replyId = reply.comment_id || 0;
                    const replyUserClass = reply.is_member ? 'member' : '';
                    const replyUserDisplay = reply.is_member ?
                        (reply.display_name || '会员用户') :
                        `匿名 ( ${reply.display_ip || '未知IP'} )`;
                    const replyDisplayTime = reply.display_time || '未知时间';
                    const replyContent = reply.comment_content || '';

                    html += `
                        <div class="reply-item">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span class="comment-user ${replyUserClass}">${replyUserDisplay}</span>
                                <div>
                                    <span class="comment-time">${replyDisplayTime}</span>
                                    ${isAdmin ? `<button class="admin-delete-btn" onclick="deleteComment(${replyId})" style="background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; margin-left: 8px; font-size: 11px;">删除</button>` : ''}
                                </div>
                            </div>
                            <div class="comment-content">${replyContent}</div>
                        </div>
                    `;
                } catch (replyError) {
                    console.error(`处理回复时出错，索引: ${index}`, replyError, reply);
                    // 继续处理其他回复
                }
            });
        }

        html += '</div>';
        return html;

    } catch (error) {
        console.error('generateCommentHtml函数执行错误:', error, comment);
        return '<div class="comment-item" style="color: #dc3545; padding: 10px;">生成评论HTML时发生错误</div>';
    }
}

// 绑定评论表单提交
function bindCommentForm() {
    const form = document.getElementById('comment-form');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        formData.append('action', 'submit_comment');

        // 验证表单
        const content = formData.get('comment_content').trim();
        if (content.length < 10) {
            alert('评论内容至少需要10个字符');
            return;
        }

        if (content.length > 500) {
            alert('评论内容不能超过500个字符');
            return;
        }

        // 提交评论
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';

        fetch('module/comment_handler.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('评论提交成功！');
                form.reset();
                // 重置星级评分为未评分状态（灰色）
                document.querySelectorAll('input[type="hidden"]').forEach(input => {
                    if (input.name.includes('quality') || input.name.includes('level')) {
                        input.value = '0';
                    }
                });
                // 重置星级显示
                document.querySelectorAll('.star-rating').forEach(rating => {
                    const stars = rating.querySelectorAll('.star');
                    updateStars(stars, 0);
                });
                initStarRating();
                // 重新加载评论
                loadComments();
            } else {
                alert(data.message || '评论提交失败');
            }
        })
        .catch(error => {
            console.error('提交评论失败:', error);
            alert('评论提交失败，请重试');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = '发表评论';
        });
    });
}

// 显示回复表单
function showReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    replyForm.style.display = 'block';
}

// 隐藏回复表单
function hideReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    replyForm.style.display = 'none';
}

// 提交回复
function submitReply(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    const textarea = replyForm.querySelector('textarea');
    const content = textarea.value.trim();

    if (content.length < 5) {
        alert('回复内容至少需要5个字符');
        return;
    }

    if (content.length > 300) {
        alert('回复内容不能超过300个字符');
        return;
    }

    const webId = document.querySelector('input[name="web_id"]').value;
    const formData = new FormData();
    formData.append('action', 'submit_reply');
    formData.append('web_id', webId);
    formData.append('parent_id', commentId);
    formData.append('comment_content', content);

    fetch('module/comment_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('回复提交成功！');
            hideReplyForm(commentId);
            textarea.value = '';
            // 重新加载评论
            loadComments();
        } else {
            alert(data.message || '回复提交失败');
        }
    })
    .catch(error => {
        console.error('提交回复失败:', error);
        alert('回复提交失败，请重试');
    });
}

// 绑定回复按钮事件
function bindReplyButtons() {
    // 这个函数在displayComments中调用，用于绑定动态生成的回复按钮
    // 实际的绑定通过onclick属性完成
}

// 检查管理员权限
function checkAdminPermission() {
    fetch('module/comment_handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=check_admin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.isAdmin = data.is_admin;
            console.log('管理员权限检查完成:', window.isAdmin ? '是管理员' : '非管理员');
        }
    })
    .catch(error => {
        console.error('检查管理员权限失败:', error);
        window.isAdmin = false;
    });
}

// 删除评论
function deleteComment(commentId) {
    try {
        if (!window.isAdmin) {
            alert('权限不足，只有管理员可以删除评论');
            return;
        }

        if (!commentId || isNaN(commentId)) {
            console.error('无效的评论ID:', commentId);
            alert('评论ID无效，无法删除');
            return;
        }

        if (!confirm('确定要删除这条评论吗？删除后无法恢复。')) {
            return;
        }

        // 显示删除中状态
        const deleteButtons = document.querySelectorAll(`button[onclick="deleteComment(${commentId})"]`);
        deleteButtons.forEach(btn => {
            btn.disabled = true;
            btn.textContent = '删除中...';
        });

        fetch('module/comment_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete_comment&comment_id=${commentId}`
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    alert('评论删除成功');
                    // 重新加载评论列表
                    loadComments();
                } else {
                    alert('删除失败：' + (data.message || '未知错误'));
                    // 恢复按钮状态
                    deleteButtons.forEach(btn => {
                        btn.disabled = false;
                        btn.textContent = '删除';
                    });
                }
            } catch (parseError) {
                console.error('JSON解析错误:', parseError);
                console.error('服务器响应:', text);
                alert('服务器响应格式错误，删除可能失败');
                // 恢复按钮状态
                deleteButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.textContent = '删除';
                });
            }
        })
        .catch(error => {
            console.error('删除评论失败:', error);
            alert('删除失败，请稍后重试：' + error.message);
            // 恢复按钮状态
            deleteButtons.forEach(btn => {
                btn.disabled = false;
                btn.textContent = '删除';
            });
        });
    } catch (error) {
        console.error('deleteComment函数执行错误:', error);
        alert('删除操作发生错误，请刷新页面后重试');
    }
}
</script>
</body>
</html>