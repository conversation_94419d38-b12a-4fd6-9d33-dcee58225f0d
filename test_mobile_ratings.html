<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端评分显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 复制的评论评分样式 */
        .comment-ratings {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .comment-ratings span {
            margin-right: 15px;
            color: #666;
        }

        .rating-row {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .comment-ratings {
                margin-bottom: 12px;
            }
            
            .rating-row {
                margin-bottom: 6px;
                font-size: 13px;
            }
            
            .comment-ratings span {
                display: block;
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
        
        /* 统计样式 */
        .stats-item {
            display: inline-block;
            margin-right: 20px;
            text-align: center;
        }

        .stats-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .stats-value {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .stats-stars {
            font-size: 14px;
            margin-top: 2px;
        }

        .stats-stars span {
            margin-right: 1px;
        }

        /* 统计信息移动端优化 */
        @media (max-width: 768px) {
            .stats-item {
                display: block;
                margin-right: 0;
                margin-bottom: 15px;
                text-align: left;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 5px;
            }
            
            .stats-label {
                font-size: 13px;
                margin-bottom: 3px;
            }
            
            .stats-value {
                font-size: 15px;
            }
            
            .stats-stars {
                font-size: 13px;
            }
        }
        
        .star {
            color: #ffc107;
        }
        
        .device-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>移动端评分显示测试</h1>
        
        <div class="device-info">
            <strong>当前设备信息：</strong>
            <span id="device-info"></span>
        </div>
        
        <div class="test-section">
            <div class="test-title">1. 评论中的评分显示（新版本 - 三行布局）</div>
            <div class="comment-ratings">
                <div class="rating-row">内容质量：<span class="star">★★★★★</span></div>
                <div class="rating-row">网站服务：<span class="star">★★★★☆</span></div>
                <div class="rating-row">网站诚信：<span class="star">★★★★★</span></div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 评论中的评分显示（旧版本 - 一行布局）</div>
            <div class="comment-ratings">
                <span>内容质量：<span class="star">★★★★★</span></span>
                <span>网站服务：<span class="star">★★★★☆</span></span>
                <span>网站诚信：<span class="star">★★★★★</span></span>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 评论统计显示</div>
            <div class="stats-container">
                <div class="stats-item">
                    <span class="stats-label">总评论数</span>
                    <span class="stats-value">25</span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">内容质量</span>
                    <span class="stats-value">4.5</span>
                    <div class="stats-stars"><span class="star">★★★★☆</span></div>
                </div>
                <div class="stats-item">
                    <span class="stats-label">网站服务</span>
                    <span class="stats-value">4.2</span>
                    <div class="stats-stars"><span class="star">★★★★☆</span></div>
                </div>
                <div class="stats-item">
                    <span class="stats-label">网站诚信</span>
                    <span class="stats-value">4.8</span>
                    <div class="stats-stars"><span class="star">★★★★★</span></div>
                </div>
                <div class="stats-item">
                    <span class="stats-label">综合评分</span>
                    <span class="stats-value">4.5</span>
                    <div class="stats-stars"><span class="star">★★★★☆</span></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 测试说明</div>
            <ul>
                <li><strong>桌面端（>768px）</strong>：评论统计横向排列，评论评分可能在一行显示</li>
                <li><strong>移动端（≤768px）</strong>：评论统计纵向排列，评论评分强制三行显示</li>
                <li><strong>小屏幕（≤480px）</strong>：进一步优化间距和字体大小</li>
            </ul>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. 修改总结</div>
            <ul>
                <li>✅ 评论中的评分改为三行显示（使用 .rating-row）</li>
                <li>✅ 添加了移动端CSS媒体查询优化</li>
                <li>✅ 评论统计在移动端改为纵向布局</li>
                <li>✅ 优化了字体大小和间距</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const width = window.innerWidth;
            let deviceType = '';
            
            if (width <= 480) {
                deviceType = '小屏手机';
            } else if (width <= 768) {
                deviceType = '移动设备';
            } else if (width <= 1024) {
                deviceType = '平板设备';
            } else {
                deviceType = '桌面设备';
            }
            
            document.getElementById('device-info').textContent = 
                `屏幕宽度: ${width}px (${deviceType})`;
        }
        
        // 页面加载和窗口大小改变时更新设备信息
        window.addEventListener('load', updateDeviceInfo);
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
