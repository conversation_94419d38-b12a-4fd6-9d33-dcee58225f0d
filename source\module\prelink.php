<?php

/** module */
function get_module_url($module = 'index')
{
    global $options;

    if ($module == 'index') {
        $strurl = $options['site_root'];
    } else {
        if ($options['link_struct'] == 1) {
            $strurl = $options['site_root'] . $module . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $options['site_root'] . $module . '/';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $options['site_root'] . $module;
        } else {
            $strurl = '?mod=' . $module;
        }
    }

    return $strurl;
}

/** category */
function get_category_url($cate_mod = 'webdir', $cate_id = 0, $page = 1)
{
    global $options;

    $cate = get_one_category($cate_id);
    $cate_dir = !empty($cate['cate_dir']) ? $cate['cate_dir'] : 'category';
    $page = isset($page) && $page > 0 ? $page : 1;

    if ($options['link_struct'] == 1) {
        $strurl = $options['site_root'] . $cate_mod . '-' . $cate_dir . '-' . $cate_id . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $options['site_root'] . $cate_mod . '/' . $cate_dir . '/' . $cate_id . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $options['site_root'] . $cate_mod . '/' . $cate_dir . '/' . $cate_id . '/' . $page;
    } else {
        $strurl = '?mod=' . $cate_mod . '&cid=' . $cate_id;
    }
    unset($cate);

    return $strurl;
}

/** update */
function get_update_url($days, $page = 1)
{
    global $options;

    $days = isset($days) && $days > 0 ? $days : 0;
    $page = isset($page) && $page > 0 ? $page : 1;

    if ($options['link_struct'] == 1) {
        $strurl = $options['site_root'] . 'update-' . $days . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $options['site_root'] . 'update/' . $days . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $options['site_root'] . 'update/' . $days . '/' . $page;
    } else {
        $strurl = '?mod=update&days=' . $days;
    }

    return $strurl;
}

/** archives */
function get_archives_url($date, $page = 1)
{
    global $options;

    $date = isset($date) && strlen($date) == 6 ? $date : 0;
    $page = isset($page) && $page > 0 ? $page : 1;

    if ($options['link_struct'] == 1) {
        $strurl = $options['site_root'] . 'archives-' . $date . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $options['site_root'] . 'archives/' . $date . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $options['site_root'] . 'archives/' . $date . '/' . $page;
    } else {
        $strurl = '?mod=archives&date=' . $date;
        if ($page > 1) {
            $strurl .= '&page=' . $page;
        }
    }

    return $strurl;
}

/** search */
function get_search_url($type, $query, $page = 1)
{
    global $options;

    $query = isset($query) && !empty($query) ? urlencode($query) : '';
    $page = isset($page) && $page > 0 ? $page : 1;

    if ($options['link_struct'] == 1) {
        $strurl = $options['site_root'] . 'search/' . $type . '-' . $query . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $options['site_root'] . 'search/' . $type . '/' . $query . '-' . $page . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $options['site_root'] . 'search/' . $type . '/' . $query . '/' . $page;
    } else {
        $strurl = '?mod=search&type=' . $type . '&query=' . $query;
        if ($page > 1) {
            $strurl .= '&page=' . $page;
        }
    }

    return $strurl;
}

/** website */
function get_website_url($web_id, $abs_path = false, $web_name = '', $web_url = '')
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    // 如果提供了网站名称和URL，生成SEO友好的URL
    if (!empty($web_name) && !empty($web_url)) {
        // 清理网站名称，只保留字母数字和中文
        $clean_name = preg_replace('/[^\w\x{4e00}-\x{9fa5}]/u', '', $web_name);
        $clean_name = mb_substr($clean_name, 0, 30, 'UTF-8'); // 限制长度

        // 清理域名
        $clean_domain = preg_replace('/^https?:\/\//', '', $web_url);
        $clean_domain = preg_replace('/^www\./', '', $clean_domain);
        $clean_domain = preg_replace('/[^a-zA-Z0-9.-]/', '', $clean_domain);
        $clean_domain = rtrim($clean_domain, '/');

        if ($options['link_struct'] == 1) {
            $strurl = $url_prefix . 'siteinfo-' . $web_id . '-' . $clean_name . '-' . $clean_domain . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $url_prefix . 'siteinfo/' . $web_id . '-' . $clean_name . '-' . $clean_domain . '.html';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $url_prefix . 'siteinfo/' . $web_id . '/' . $clean_name . '/' . $clean_domain;
        } else {
            $strurl = $url_prefix . '?mod=siteinfo&wid=' . $web_id;
        }
    } else {
        // 保持原有的简单URL结构作为后备
        if ($options['link_struct'] == 1) {
            $strurl = $url_prefix . 'siteinfo-' . $web_id . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $url_prefix . 'siteinfo/' . $web_id . '.html';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $url_prefix . 'siteinfo/' . $web_id;
        } else {
            $strurl = $url_prefix . '?mod=siteinfo&wid=' . $web_id;
        }
    }

    return $strurl;
}

/** article */
function get_article_url($art_id, $abs_path = false, $art_title = '')
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    // 如果提供了文章标题，生成SEO友好的URL
    if (!empty($art_title) && $options['link_struct'] > 0) {
        // 清理文章标题，只保留字母数字和中文
        $clean_title = preg_replace('/[^\w\x{4e00}-\x{9fa5}]/u', '', $art_title);
        $clean_title = mb_substr($clean_title, 0, 50, 'UTF-8'); // 限制长度

        if (!empty($clean_title)) {
            if ($options['link_struct'] == 1) {
                $strurl = $url_prefix . 'article-' . $art_id . '-' . $clean_title . '.html';
            } elseif ($options['link_struct'] == 2) {
                $strurl = $url_prefix . 'article/' . $art_id . '-' . $clean_title . '.html';
            } elseif ($options['link_struct'] == 3) {
                $strurl = $url_prefix . 'article/' . $art_id . '-' . $clean_title;
            } else {
                $strurl = $url_prefix . '?mod=artinfo&aid=' . $art_id;
            }
            return $strurl;
        }
    }

    // 保持原有的简单URL结构作为后备
    if ($options['link_struct'] == 1) {
        $strurl = $url_prefix . 'artinfo-' . $art_id . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $url_prefix . 'artinfo/' . $art_id . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $url_prefix . 'artinfo/' . $art_id;
    } else {
        $strurl = $url_prefix . '?mod=artinfo&aid=' . $art_id;
    }

    return $strurl;
}

/** VIP相关URL生成函数 */
function get_vip_list_url($cate_id = 0, $page = 1, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    if ($cate_id > 0) {
        if ($page > 1) {
            $strurl = $url_prefix . 'vip/category/' . $cate_id . '/' . $page . '/';
        } else {
            $strurl = $url_prefix . 'vip/category/' . $cate_id . '/';
        }
    } else {
        if ($page > 1) {
            $strurl = $url_prefix . 'vip/list/' . $page . '/';
        } else {
            $strurl = $url_prefix . 'vip/';
        }
    }

    return $strurl;
}

function get_vip_detail_url($vip_id, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    $strurl = $url_prefix . 'vip/detail/' . $vip_id . '/';
    return $strurl;
}

/** 黑名单相关URL生成函数 */
function get_blacklist_url($category = 0, $page = 1, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    if ($category > 0) {
        if ($page > 1) {
            $strurl = $url_prefix . 'blacklist/category/' . $category . '/' . $page . '/';
        } else {
            $strurl = $url_prefix . 'blacklist/category/' . $category . '/';
        }
    } else {
        if ($page > 1) {
            $strurl = $url_prefix . 'blacklist/' . $page . '/';
        } else {
            $strurl = $url_prefix . 'blacklist/';
        }
    }

    return $strurl;
}

function get_blacklist_detail_url($blacklist_id, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    $strurl = $url_prefix . 'blacklist/detail/' . $blacklist_id . '/';
    return $strurl;
}

/** 待审核相关URL生成函数 */
function get_pending_url($cate_id = 0, $page = 1, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    if ($cate_id > 0) {
        if ($page > 1) {
            $strurl = $url_prefix . 'pending/category/' . $cate_id . '/' . $page . '/';
        } else {
            $strurl = $url_prefix . 'pending/category/' . $cate_id . '/';
        }
    } else {
        if ($page > 1) {
            $strurl = $url_prefix . 'pending/' . $page . '/';
        } else {
            $strurl = $url_prefix . 'pending/';
        }
    }

    return $strurl;
}

function get_pending_detail_url($pending_id, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    $strurl = $url_prefix . 'pending/detail/' . $pending_id . '/';
    return $strurl;
}

/** weblink */
function get_weblink_url($link_id, $abs_path = false)
{
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    if ($options['link_struct'] == 1) {
        $strurl = $url_prefix . 'linkinfo-' . $link_id . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $url_prefix . 'linkinfo/' . $link_id . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $url_prefix . 'linkinfo/' . $link_id;
    } else {
        $strurl = $url_prefix . '?mod=linkinfo&lid=' . $link_id;
    }

    return $strurl;
}

/** diypage */
function get_diypage_url($page_id)
{
    global $options;

    if ($options['link_struct'] == 1) {
        $strurl = $options['site_root'] . 'diypage-' . $page_id . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $options['site_root'] . 'diypage/' . $page_id . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $options['site_root'] . 'diypage/' . $page_id;
    } else {
        $strurl = '?mod=diypage&pid=' . $page_id;
    }

    return $strurl;
}

/** rssfeed */
function get_rssfeed_url($module, $cate_id)
{
    global $options;

    if ($cate_id > 0) {
        if ($options['link_struct'] == 1) {
            $strurl = $options['site_root'] . 'rssfeed-' . $module . '-' . $cate_id . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $options['site_root'] . 'rssfeed/' . $module . '/' . $cate_id . '.html';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $options['site_root'] . 'rssfeed/' . $module . '/' . $cate_id;
        } else {
            $strurl = '?mod=rssfeed&type=' . $module . '&cid=' . $cate_id;
        }
    } else {
        if ($options['link_struct'] == 1) {
            $strurl = $options['site_root'] . 'rssfeed-' . $module . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $options['site_root'] . 'rssfeed/' . $module . '/';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $options['site_root'] . 'rssfeed/' . $module;
        } else {
            $strurl = '?mod=rssfeed&type=' . $module;
        }
    }

    return $strurl;
}

/** sitemap */
function get_sitemap_url($module, $cate_id)
{
    global $options;

    if ($cate_id > 0) {
        if ($options['link_struct'] == 1) {
            $strurl = $options['site_root'] . 'sitemap-' . $module . '-' . $cate_id . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $options['site_root'] . 'sitemap/' . $module . '/' . $cate_id . '.html';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $options['site_root'] . 'sitemap/' . $module . '/' . $cate_id;
        } else {
            $strurl = '?mod=sitemap&type=' . $module . '&cid=' . $cate_id;
        }
    } else {
        if ($options['link_struct'] == 1) {
            $strurl = $options['site_root'] . 'sitemap-' . $module . '.html';
        } elseif ($options['link_struct'] == 2) {
            $strurl = $options['site_root'] . 'sitemap/' . $module . '';
        } elseif ($options['link_struct'] == 3) {
            $strurl = $options['site_root'] . 'sitemap/' . $module;
        } else {
            $strurl = '?mod=sitemap&type=' . $module;
        }
    }

    return $strurl;
}


/** thumbs */
function get_webthumb($web_pic) {
    global $options;
    
    if (!empty($web_pic)) {
        // 检查是否为外部链接（以http://或https://开头）
        if (preg_match('/^https?:\/\//i', $web_pic)) {
            return $web_pic; // 外部链接直接返回，不加前缀
        }
        // 否则拼接本地路径
        $strurl = $options['site_root'] . $options['upload_dir'] . '/' . $web_pic;
    } else {
        $strurl = $options['site_root'] . 'public/images/nopic.gif';
    }
    
    return $strurl;
}

?>